"""
Board query service module.
Handles dahanghai flow analysis, board statistics, and related board data queries.
"""

from datetime import datetime
from typing import Dict, Optional

from vups.logger import logger
from vups_server.base.query_base import BaseQueryService


class BoardQueryService(BaseQueryService):
    """Service for board-related queries including flow analysis and statistics."""

    def __init__(self):
        super().__init__(cache_ttl=600)  # 10 minutes cache for board data


    async def get_target_flow_analysis(
        self,
        target_vtube_name: str,
        current_time_str: str,
        previous_time_str: str,
        n: int = 5
    ) -> Optional[Dict]:
        """
        Analyze user flow for a target vtuber between two time points.

        Args:
            target_vtube_name: Name of the target vtuber
            current_time_str: Current time point as string
            previous_time_str: Previous time point as string
            n: Number of top flow sources/targets to return

        Returns:
            Dictionary containing inflow and outflow data, or None if error occurred
        """
        cache_key = f"target_flow_{target_vtube_name}_{current_time_str}_{previous_time_str}_{n}"

        flow_analysis_query = """
            WITH target_current AS (
                SELECT uid, username, face
                FROM dahanghai_list_table
                WHERE time::text = $2 AND up_name = $1
            ),
            target_previous AS (
                SELECT uid, username, face
                FROM dahanghai_list_table
                WHERE time::text = $3 AND up_name = $1
            ),
            all_current AS (
                SELECT up_name, uid, username, face
                FROM dahanghai_list_table
                WHERE time::text = $2 AND up_name != $1
            ),
            all_previous AS (
                SELECT up_name, uid, username, face
                FROM dahanghai_list_table
                WHERE time::text = $3 AND up_name != $1
            ),
            -- Users who flowed IN to target (in current but not in previous)
            flowed_in_users AS (
                SELECT tc.uid, tc.username, tc.face
                FROM target_current tc
                LEFT JOIN target_previous tp ON tc.uid = tp.uid
                WHERE tp.uid IS NULL
            ),
            -- Users who flowed OUT from target (in previous but not in current)
            flowed_out_users AS (
                SELECT tp.uid, tp.username, tp.face
                FROM target_previous tp
                LEFT JOIN target_current tc ON tp.uid = tc.uid
                WHERE tc.uid IS NULL
            ),
            -- Find where flowed_in users came from
            inflow_sources AS (
                SELECT
                    ap.up_name,
                    COUNT(*) as user_count,
                    ARRAY_AGG(ARRAY[ap.uid::text, ap.username, ap.face]) as users
                FROM flowed_in_users fiu
                INNER JOIN all_previous ap ON fiu.uid = ap.uid
                GROUP BY ap.up_name
                ORDER BY user_count DESC
                LIMIT $4
            ),
            -- Find where flowed_out users went to
            outflow_targets AS (
                SELECT
                    ac.up_name,
                    COUNT(*) as user_count,
                    ARRAY_AGG(ARRAY[ac.uid::text, ac.username, ac.face]) as users
                FROM flowed_out_users fou
                INNER JOIN all_current ac ON fou.uid = ac.uid
                GROUP BY ac.up_name
                ORDER BY user_count DESC
                LIMIT $4
            )
            SELECT
                'in' as flow_type, up_name, users
            FROM inflow_sources
            UNION ALL
            SELECT
                'out' as flow_type, up_name, users
            FROM outflow_targets
        """

        flow_results = await self._cached_query(
            cache_key=cache_key,
            query=flow_analysis_query,
            params=[target_vtube_name, current_time_str, previous_time_str, n],
            fetch_type="fetch"
        )

        if flow_results is None:
            return None

        # Process results into expected format
        result = {'in': {}, 'out': {}}

        for row in flow_results:
            flow_type = row['flow_type']
            up_name = row['up_name']
            users = row['users']

            # Convert array format to expected tuple format
            user_tuples = []
            for user_array in users:
                if len(user_array) >= 3:
                    user_tuples.append((int(user_array[0]), user_array[1], user_array[2]))

            result[flow_type][up_name] = user_tuples

        return result

    async def get_target_flow_analysis_by_period(
        self,
        target_vtube_name: str,
        start_time_str1: str,
        end_time_str1: str,
        start_time_str2: str,
        end_time_str2: str,
        n: int = 5
    ) -> Optional[Dict]:
        """
        Analyze user flow for a target vtuber between two time periods.

        Args:
            target_vtube_name: Name of the target vtuber
            start_time_str1: Start date of first period (YYYY-MM-DD)
            end_time_str1: End date of first period (YYYY-MM-DD)
            start_time_str2: Start date of second period (YYYY-MM-DD)
            end_time_str2: End date of second period (YYYY-MM-DD)
            n: Number of top flow sources/targets to return

        Returns:
            Dictionary containing inflow and outflow data, or None if error occurred
        """
        cache_key = f"target_flow_period_{target_vtube_name}_{start_time_str1}_{end_time_str1}_{start_time_str2}_{end_time_str2}_{n}"

        try:
            # Convert date strings to datetime.date objects
            start_date1 = datetime.strptime(start_time_str1, '%Y-%m-%d').date()
            end_date1 = datetime.strptime(end_time_str1, '%Y-%m-%d').date()
            start_date2 = datetime.strptime(start_time_str2, '%Y-%m-%d').date()
            end_date2 = datetime.strptime(end_time_str2, '%Y-%m-%d').date()

            period_flow_query = """
                WITH target_period1 AS (
                    SELECT DISTINCT uid, username, face
                    FROM dahanghai_list_table
                    WHERE time BETWEEN $2 AND $3
                    AND up_name = $1
                ),
                target_period2 AS (
                    SELECT DISTINCT uid, username, face
                    FROM dahanghai_list_table
                    WHERE time BETWEEN $4 AND $5
                    AND up_name = $1
                ),
                all_period1 AS (
                    SELECT DISTINCT up_name, uid, username, face
                    FROM dahanghai_list_table
                    WHERE time BETWEEN $2 AND $3
                    AND up_name != $1
                ),
                all_period2 AS (
                    SELECT DISTINCT up_name, uid, username, face
                    FROM dahanghai_list_table
                    WHERE time BETWEEN $4 AND $5
                    AND up_name != $1
                ),
                -- Users who flowed IN to target (in period2 but not in period1)
                flowed_in_users AS (
                    SELECT tp2.uid, tp2.username, tp2.face
                    FROM target_period2 tp2
                    LEFT JOIN target_period1 tp1 ON tp2.uid = tp1.uid
                    WHERE tp1.uid IS NULL
                ),
                -- Users who flowed OUT from target (in period1 but not in period2)
                flowed_out_users AS (
                    SELECT tp1.uid, tp1.username, tp1.face
                    FROM target_period1 tp1
                    LEFT JOIN target_period2 tp2 ON tp1.uid = tp2.uid
                    WHERE tp2.uid IS NULL
                ),
                -- Find where flowed_in users came from
                inflow_sources AS (
                    SELECT
                        ap1.up_name,
                        COUNT(*) as user_count,
                        ARRAY_AGG(ARRAY[ap1.uid::text, ap1.username, ap1.face]) as users
                    FROM flowed_in_users fiu
                    INNER JOIN all_period1 ap1 ON fiu.uid = ap1.uid
                    GROUP BY ap1.up_name
                    ORDER BY user_count DESC
                    LIMIT $6
                ),
                -- Find where flowed_out users went to
                outflow_targets AS (
                    SELECT
                        ap2.up_name,
                        COUNT(*) as user_count,
                        ARRAY_AGG(ARRAY[ap2.uid::text, ap2.username, ap2.face]) as users
                    FROM flowed_out_users fou
                    INNER JOIN all_period2 ap2 ON fou.uid = ap2.uid
                    GROUP BY ap2.up_name
                    ORDER BY user_count DESC
                    LIMIT $6
                )
                SELECT
                    'in' as flow_type, up_name, users
                FROM inflow_sources
                UNION ALL
                SELECT
                    'out' as flow_type, up_name, users
                FROM outflow_targets
            """

            flow_results = await self._cached_query(
                cache_key=cache_key,
                query=period_flow_query,
                params=[target_vtube_name, start_date1, end_date1, start_date2, end_date2, n],
                fetch_type="fetch"
            )

            if flow_results is None:
                return None

            # Process results into expected format
            result = {'in': {}, 'out': {}}

            for row in flow_results:
                flow_type = row['flow_type']
                up_name = row['up_name']
                users = row['users']

                # Convert array format to expected tuple format
                user_tuples = []
                for user_array in users:
                    if len(user_array) >= 3:
                        user_tuples.append((int(user_array[0]), user_array[1], user_array[2]))

                result[flow_type][up_name] = user_tuples

            return result

        except ValueError as e:
            logger.error(f"Invalid date format in period flow analysis: {e}")
            return None
        except Exception as e:
            logger.error(f"Error in period flow analysis: {e}")
            return None

    async def get_dahanghai_table_stats(self) -> Dict:
        """
        Get statistics about dahanghai_list_table for monitoring.

        Returns:
            Dictionary containing table statistics
        """
        cache_key = "dahanghai_table_stats"

        stats_query = """
            SELECT
                COUNT(*) as total_records,
                COUNT(DISTINCT up_name) as unique_vtubers,
                COUNT(DISTINCT uid) as unique_users,
                MIN(time) as earliest_date,
                MAX(time) as latest_date
            FROM dahanghai_list_table
        """

        stats_result = await self._cached_query(
            cache_key=cache_key,
            query=stats_query,
            params=[],
            fetch_type="fetchrow"
        )

        if stats_result:
            return dict(stats_result)
        return {}

    async def clear_cache(self) -> None:
        """Clear all cached data for this service."""
        await self.cache.clear()


# Global instance for easy access
board_query_service = BoardQueryService()


# ============================================================================
# BACKWARD COMPATIBILITY FUNCTIONS
# ============================================================================

async def collect_target_flow_with_target_vtuber_optimized(
    target_vtube_name: str,
    current_time_str: str,
    previous_time_str: str,
    n: int = 5
):
    """
    DEPRECATED: Use BoardQueryService.get_target_flow_analysis() instead.

    Backward compatibility wrapper for existing code.
    """
    return await board_query_service.get_target_flow_analysis(
        target_vtube_name, current_time_str, previous_time_str, n
    )


async def collect_target_flow_with_target_vtuber_by_period_optimized(
    target_vtube_name: str,
    start_time_str1: str,
    end_time_str1: str,
    start_time_str2: str,
    end_time_str2: str,
    n: int = 5
):
    """
    DEPRECATED: Use BoardQueryService.get_target_flow_analysis_by_period() instead.

    Backward compatibility wrapper for existing code.
    """
    return await board_query_service.get_target_flow_analysis_by_period(
        target_vtube_name, start_time_str1, end_time_str1,
        start_time_str2, end_time_str2, n
    )


async def get_dahanghai_table_stats():
    """
    DEPRECATED: Use BoardQueryService.get_dahanghai_table_stats() instead.

    Backward compatibility wrapper for existing code.
    """
    return await board_query_service.get_dahanghai_table_stats()


def clear_flow_analysis_cache():
    """
    DEPRECATED: Use BoardQueryService.clear_cache() instead.

    Backward compatibility wrapper for existing code.
    """
    import asyncio
    asyncio.create_task(board_query_service.clear_cache())
